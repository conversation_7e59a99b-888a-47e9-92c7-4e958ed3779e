[app_settings]
app_name = "fabric-shell"
debug = false

window_manager = "hyprland"

styles_dir = "styles"
icons_dir = "icons"
artwork_cache_dir = "user_cache_dir-artworks"
wallpapers_dir = ""
wallpapers_thumbnails_cache_dir = ""

thumbnails_generator_max_workers = 4

sass_compiler_command = "sass {input} {output} --no-source-map"

confirm_icon = "&#xea5e;"
cancel_icon = "&#xeb55;"

power_menu_icon = "&#xeb0d;"
power_menu_action_lock_icon = "&#xeae2;"
power_menu_action_suspend_icon = "&#xeaf8;"
power_menu_action_reboot_icon = "&#xebb4;"
power_menu_action_shut_down_icon = "&#xeb0d;"

power_menu_lock_commands = [
    "playerctl pause",
    "loginctl lock-session",
    "sleep 4",
]
power_menu_suspend_commands = [
    "systemctl suspend",
]
power_menu_reboot_commands = [
    "systemctl reboot",
]
power_menu_shutdown_commands = [
    "systemctl poweroff",
]

pill_stack_transition_duration = 300
pill_revealer_animation_duration = 250
osd_revealer_animation_duration = 250
bar_revealer_animation_duration = 250

media_player_artwork_download_command = "curl -s '{url}' -o '{path}'"
media_player_artwork_size = 120
media_player_icon_size = 16
media_player_no_artwork_icon_size = 40
media_player_previous_icon = "&#xf693;"
media_player_next_icon = "&#xf694;"
media_player_pause_icon = "&#xf690;"
media_player_play_icon = "&#xf691;"
media_player_shuffle_icon = "&#xf000;"
media_player_no_shuffle_icon = "&#xedb3;"
media_player_repeat_none_icon = "&#xf18e;"
media_player_repeat_playlist_icon = "&#xeb72;"
media_player_repeat_track_icon = "&#xeb71;"
media_player_allowed_players = { "spotify" = "󰓇" }
bar_widgets = { "left" = [
    "workspaces",
    "wallpaper",
    "key_layout",
    "recorder",
], "right" = [
    "tray",
    "battery",
    "res_monitor",
    "net_monitor",
] }

battery_widget_compact = false
battery_widget_headset_icon = "&#xfa3c;"
battery_widget_battery_full_icon = "&#xff19;"
battery_widget_battery_half_full_icon = "&#xff1b;"
battery_widget_battery_empty_icon = "&#xff15;"
battery_widget_battery_charging_icon = "&#x10021;"
battery_widget_battery_unknown_icon = "&#xf671;"
battery_widget_tooltip_markup = "{icon} {name}: {percentage}\n{state}"
battery_list_devices_command = "upower -e"
battery_device_info_command = "upower -i {device}"

tray_icon_size = 16

chevron_left = "&#xea60;"
chevron_right = "&#xea61;"
chevron_up = "&#xea62;"
chevron_down = "&#xea5f;"

brightness_list_devices_command = "brightnessctl -ml"
get_brightness_command = "brightnessctl -md {device} g"
set_brightness_command = "brightnessctl -md {device} s {value}"
brightness_inc_command = "brightnessctl -md {device} s +{delta}"
brightness_dec_command = "brightnessctl -md {device} s {delta}-"
auto_brightness_check_command = "systemctl --user is-active wluma"
auto_brightness_start_command = "systemctl --user start wluma --now"
auto_brightness_stop_command = "systemctl --user stop wluma"

notification_low_timeout = 2
notification_low_icon = "&#xf6d8;"
notification_low_icon_color = "blue"
notification_normal_timeout = 4
notification_normal_icon = "&#xf6d9;"
notification_normal_icon_color = "green"
notification_urgent_timeout = 0
notification_urgent_icon = "&#xfa44;"
notification_urgent_icon_color = "red"
notification_app_icon_size = 16
notification_dismiss_icon = "&#xeb55;"
notification_max_actions = 4

popup_notification_max_notifications = 3
popup_notification_new_notification_pos = "top"

qs_brightness_high_threshold = 40
qs_volume_high_threshold = 50

# brightness_high_icon = "󰃠"
# brightness_low_icon = "󰃟"
# auto_brightness_icon = "󰃡"
brightness_high_icon = "&#xfb24;"
brightness_low_icon = "&#xfb23;"
brightness_off_icon = "&#xed63;"
auto_brightness_icon = "&#xfd99;"

volume_high_icon = "&#xeb51;"
volume_low_icon = "&#xeb4f;"
volume_off_icon = "&#xeb50;"
volume_muted_icon = "&#xf1c3;"

microphone_on_icon = "&#xfe0f;"
microphone_off_icon = "&#xeaf0;"
microphone_muted_icon = "&#xed16;"

dnd_on_icon = "&#xf684;"
dnd_off_icon = "&#xf162;"

caffeine_on_icon = "&#xef28;"
caffeine_off_icon = "&#xf10d;"

workspaces_widget_num_workspaces = 6

resource_monitor_poll_interval = 2000
resource_monitor_overlay_icon = "&#xf692;"
resource_monitor_cpu_icon = " "
resource_monitor_memory_icon = " "
resource_monitor_tooltip_markup = "{name}: {percentage}"

wifi_connected_icon = "&#xeb52;"
ethernet_connected_icon = "&#xf00a;"
network_connecting_icon = "&#xed28;"
network_disconnected_icon = "&#xf1ca;"

bluetooth_connected_icon = "&#xecea;"
bluetooth_connecting_icon = "&#xea37;"
bluetooth_disconnected_icon = "&#xeceb;"

network_usage_download_icon = "&#x10068;"
network_usage_upload_icon = "&#x10065;"

nmcli_command = "nmcli -c no -t"

screen_records_dir = "user_videos_dir-Screen_Recordings"

screen_record_command = "wf-recorder -c hevc_vaapi -F scale_vaapi=format=nv12"
screen_record_audio_option = "--audio="
screen_record_portion_option = "-g"
screen_record_portion_command = "slurp"
screen_record_output_option = "-f"
screen_record_widget_record_icon = "&#xf671;"
screen_record_widget_stop_icon = "&#xf6a5;"
screen_record_widget_mic_icon = "&#xeaf0;"
screen_record_widget_speakers_icon = "&#xeb51;"
screen_record_widget_no_audio_icon = "&#xf1c3;"

osd_timeout = 2
osd_brightness_delta = 0.05
osd_volume_delta = 5

change_wallpaper_command = ""

wallpaper_selector_icon = "&#xeb0a;"

battery_warning_level = 20
battery_hibernate_level = 10

circular_progress_empty_part = "bottom"
circular_progress_empty_angle = 100

speakers_header_text = "Select Output Device:"
speakers_tab_icon = "&#xed61;"
speakers_unknown_icon = "&#xea8b;"
speakers_built_in_icon = "&#xea8b;"
speakers_headphones_icon = "&#xfa3c;"
microphones_header_text = "Select Input Device:"
microphones_icon = "&#xeaf0;"
microphones_tab_icon = "&#xef2c;"

app_launcher_columns = 6
app_launcher_rows = 3
app_launcher_icon_size = 36

music_ticker_timeout = 5
music_ticker_players = ["spotify"]
music_ticker_artwork_rotation_speed = 0.15

dashboard_widgets = ["calendar", "media-player"]
dashboard_qs = { "tiles" = [ [ "bluetooth", "wifi"] ], "sliders" = [ "volume-d", "brightness" ] }

dock_icon_size = 32
dock_visibility_rule = "hide when obstructed" # always visible - auto hide - hide when obstructed
dock_flash_on_app_added = true
dock_hide_delay = 0.3

[css_settings]
text_font_family = "Lexend"
text_font_size = "14px"
text_font_weight = "bold"
icon_font_family = "tabler-icons"

bar_height = "50px"
bar_padding = "8px"
bar_radius = "18px"

pill_height = "50px"
pill_padding = "10px"
pill_expanded_padding = "18px"
pill_expanded_window_radius = "32px"
pill_expanded_width = "450px"

media_player_slider_inner_padding = "8px"
media_player_slider_padding_left = "15px"

media_title_font_size = "18px"
media_artist_album_font_size = "10px"
media_artwork_size = "120px"

qs_row_height = "64px"

wallpaper_file = "\"\""

power_menu_action_size = "100px"

osd_padding = "10px"
osd_height = "300px"
osd_pos_vertical = "170px"

thick_slider_length = "250px"
thick_slider_thickness = "48px"
thick_slider_padding = "4px"
thick_slider_toggle_spacing = "12px"

wallpaper_selector_wallpaper_height = "75px"
wallpaper_selector_wallpaper_border_radius = "36px"

notification_image_size = "100px"

dock_padding = "9px"
dock_rounding = "18px"