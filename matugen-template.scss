$source: {{ colors.source_color.default.hex }};
$background: {{ colors.background.default.hex }};
$primary: {{ colors.primary.default.hex }};
$inverse_primary: {{ colors.inverse_primary.default.hex }};
$on_primary: {{ colors.on_primary.default.hex }};
$primary_container: {{ colors.primary_container.default.hex }};
$on_primary_container: {{ colors.on_primary_container.default.hex }};
$secondary: {{ colors.secondary.default.hex }};
$on_secondary: {{ colors.on_secondary.default.hex }};
$secondary_container: {{ colors.secondary_container.default.hex }};
$on_secondary_container: {{ colors.on_secondary_container.default.hex }};
$tertiary: {{ colors.tertiary.default.hex }};
$on_tertiary: {{ colors.on_tertiary.default.hex }};
$tertiary_container: {{ colors.tertiary_container.default.hex }};
$on_tertiary_container: {{ colors.on_tertiary_container.default.hex }};
$error: {{ colors.error.default.hex }};
$on_error: {{ colors.on_error.default.hex }};
$error_container: {{ colors.error_container.default.hex }};
$on_error_container: {{ colors.on_error_container.default.hex }};
$surface: {{ colors.surface.default.hex }};
$surface_bright: {{ colors.surface_bright.default.hex }};
$surface_container: {{ colors.surface_container.default.hex }};
$surface_container_high: {{ colors.surface_container_high.default.hex }};
$surface_container_highest: {{ colors.surface_container_highest.default.hex }};
$surface_container_low: {{ colors.surface_container_low.default.hex }};
$surface_container_lowest: {{ colors.surface_container_lowest.default.hex }};
$surface_dim: {{ colors.surface_dim.default.hex }};
$surface_tint: {{ colors.surface_tint.default.hex }};
$surface_variant: {{ colors.surface_variant.default.hex }};
$on_surface: {{ colors.on_surface.default.hex }};
$on_surface_variant: {{ colors.on_surface_variant.default.hex }};
$inverse_surface: {{ colors.inverse_surface.default.hex }};
$inverse_on_surface: {{ colors.inverse_on_surface.default.hex }};
