[project]
name = "fabric-shell"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fabric",
    "loguru>=0.7.3",
    "pillow>=11.1.0",
    "platformdirs>=4.3.6",
    "psutil>=6.1.1",
    "pyxdg>=0.28",
    "sdbus>=0.13.0",
    "sdbus-networkmanager>=2.0.0",
    "toml>=0.10.2",
]

[tool.uv.sources]
fabric = { git = "https://github.com/Fabric-Development/fabric.git", rev = "88fccbe46ca98102ab4a25cb36d5c48e6f993dc2" }
